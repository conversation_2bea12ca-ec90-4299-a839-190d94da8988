/**
 * ClickHouse helpers using the official JS client via Deno's npm compatibility.
 * Requires env vars: CLICKHOUSE_URL, CLICKHOUSE_USER, CLICKHOUSE_PASSWORD.
 */

import { createClient } from "https://esm.sh/@clickhouse/client-web@1.12.0";

const CLICKHOUSE_URL = Deno.env.get("CLICKHOUSE_URL");
const CLICKHOUSE_USER = Deno.env.get("CLICKHOUSE_USER");
const CLICKHOUSE_PASSWORD = Deno.env.get("CLICKHOUSE_PASSWORD");

const ch = createClient({
  url: CLICKHOUSE_URL,       // note: 'url', not 'host'
  username: <PERSON><PERSON><PERSON><PERSON>HOUSE_USER,
  password: CLICKHOUSE_PASSWORD,
});

type CHJson<D, T = never> = { data: D[]; totals?: T };

export async function queryClickHouse<D, T = never>(
  query: string,
  params: Record<string, unknown> = {},
): Promise<CHJson<D, T>> {
  const rs = await ch.query({ query, format: "JSON", query_params: params });
  return rs.json() as Promise<CHJson<D, T>>;

}