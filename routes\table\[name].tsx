import { Hand<PERSON>, PageProps } from "$fresh/server.ts";
import { loadTableConfig, getConfiguredTable, getTableConfigs } from "../../utils/tableConfig.ts";

interface TableColumn {
  key: string;
  title: string;
  label?: string;
  isNumeric: boolean;
  format?: string;
}

interface TableData {
  name: string;
  displayName: string;
  data: {
    columns: TableColumn[];
    rows: Array<Record<string, unknown>>;
    totalsRow: Record<string, unknown> | null;
    totalsPlacement: 'top' | 'bottom' | 'none';
  } | null;
  error?: string;
}

export const handler: Handlers<TableData> = {
  async GET(_req, ctx) {
    const { name } = ctx.params;
    const displayName = name.replace(/_/g, ' ');
    
    try {
      // Find and load the specific table config
      const configs = await getTableConfigs();
      const config = configs.find((c: { name: string }) => c.name === name);
      
      if (!config) {
        return new Response("Table not found", { status: 404 });
      }
      
      // Load and process the table data
      const tableConfig = await loadTableConfig(new URL(config.path, import.meta.url));
      const { columns, rows, totalsRow, totalsPlacement } = await getConfiguredTable(tableConfig);
      
      return ctx.render({
        name,
        displayName,
        data: {
          columns: columns.map(col => {
            const column: TableColumn = {
              key: col.key,
              title: col.title,
              isNumeric: col.isNumeric ?? false,
            };
            
            if (col.label) column.label = String(col.label);
            if (col.format) column.format = String(col.format);
            
            return column;
          }),
          rows,
          totalsRow: totalsRow || null,
          totalsPlacement
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error loading table ${name}:`, error);
      return ctx.render({
        name,
        displayName,
        data: null,
        error: errorMessage
      });
    }
  },
};

export default function TableView({ data }: PageProps<TableData>) {
  const { displayName, data: tableData, error } = data;
  
  if (error) {
    return (
      <div class="container">
        <h1>{displayName}</h1>
        <div class="alert alert-error">
          Error loading table: {error}
        </div>
        <a href="/table" class="btn btn-link">← Back to tables</a>
      </div>
    );
  }
  
  if (!tableData) {
    return (
      <div class="container">
        <h1>{displayName}</h1>
        <div class="alert">Loading table data...</div>
      </div>
    );
  }
  
  const { columns, rows, totalsRow, totalsPlacement } = tableData;
  
  const formatValue = (value: unknown, format?: string): string => {
    if (value === null || value === undefined) return '';
    if (typeof value !== 'number') return String(value);
    
    try {
      if (format) {
        if (format.includes('%')) {
          return new Intl.NumberFormat(undefined, { 
            style: 'percent',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
          }).format(value / 100);
        }
        if (format.includes('$')) {
          return new Intl.NumberFormat(undefined, { 
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
          }).format(value);
        }
      }
      return value.toLocaleString();
    } catch {
      return String(value);
    }
  };
  
  return (
    <div class="container">
      <div class="flex justify-between items-center mb-4">
        <h1>{displayName}</h1>
        <a href="/table" class="btn btn-link">← Back to tables</a>
      </div>
      
      <div class="overflow-x-auto">
        <table class="table">
          <thead>
            <tr>
              {columns.map((col) => (
                <th key={col.key} class={col.isNumeric ? 'text-right' : ''}>
                  {col.title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {totalsPlacement === 'top' && totalsRow && (
              <tr class="font-bold bg-gray-100">
                {columns.map((col) => (
                  <td key={`total-${col.key}`} class={col.isNumeric ? 'text-right' : ''}>
                    {formatValue(totalsRow[col.key], col.format)}
                  </td>
                ))}
              </tr>
            )}
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {columns.map((col) => (
                  <td key={`${rowIndex}-${col.key}`} class={col.isNumeric ? 'text-right' : ''}>
                    {formatValue(row[col.key], col.format)}
                  </td>
                ))}
              </tr>
            ))}
            {totalsPlacement === 'bottom' && totalsRow && (
              <tr class="font-bold bg-gray-100">
                {columns.map((col) => (
                  <td key={`total-${col.key}`} class={col.isNumeric ? 'text-right' : ''}>
                    {formatValue(totalsRow[col.key], col.format)}
                  </td>
                ))}
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
