import { Handlers, PageProps } from "$fresh/server.ts";
import { getTableConfigs } from "../../utils/tableConfig.ts";

interface TableListProps {
  tables: { name: string; displayName: string }[];
}

export const handler: Handlers<TableListProps> = {
  async GET(_, ctx) {
    const configs = await getTableConfigs();
    const tables = configs.map(config => ({
      name: config.name,
      displayName: config.name.replace(/_/g, ' ')
    }));
    
    return ctx.render({ tables });
  },
};

export default function TableList({ data }: PageProps<TableListProps>) {
  const { tables } = data;
  
  return (
    <div class="container">
      <h1>Available Tables</h1>
      <ul>
        {tables.map((table) => (
          <li key={table.name}>
            <a href={`/table/${table.name}`} class="btn btn-link">
              {table.displayName}
            </a>
          </li>
        ))}
      </ul>
    </div>
  );
}
