# Adams Distributing Dashboard Example – Fresh
A lightweight, declarative dashboard built with Deno's Fresh framework to display sales data in a responsive table. Configure the table via a YAML file to fetch and render only the data you need—without hardcoding queries.

## Quick Start
```bash
deno run -A -r https://fresh.deno.dev .
deno task start
```

## Purpose
This project showcases a simple, secure, and scalable web dashboard using Deno's Fresh framework. It connects to a ClickHouse database hosted on Elestio, ensuring fast analytics queries while keeping credentials secure and never exposing them to the client.

## Tech Stack
- ClickHouse — Cloud-hosted columnar database for high-performance analytics queries (via Elestio)
- Elestio — Managed hosting provider for ClickHouse and other services
- Fresh — Full-stack web framework for Deno
- Pico.css — Minimal CSS framework for clean, responsive design

## Features (MVP)
- Declarative configuration via YAML files in the `table-configs` directory for easy customization
- Secure backend queries to ClickHouse—client never connects directly
- No database credentials exposed in the frontend
- Excel-style number formatting for values (e.g., `#,##0.00`, `$#,##0.00`)
- Grand totals at the top or bottom of the table (or omitted)

## Configuration
Table configurations are stored as YAML files in the `table-configs` directory. The default configuration file is `table-configs/Sales_Team.table-config.yaml`.

### Configuration Format
- **`table`**: the source table (e.g., `default.sales`).
- **`dimensions`**: grouping fields (e.g., `Sales Team`, `Sales Manager`).
- **`columns`**: values with an aggregation and Excel-style number format (e.g., `sum(Revenue)` with `$#,##0.00`).
- **`sort`**: default ordering (e.g., by `Sales Team` then `Sales Manager`).
- **`totals`**: grand total placement — `top`, `bottom`, or `none`.
- **`filters`**: a parameterized `WHERE` clause plus its parameters (e.g., `` `Ship Year` = {year:String} `` with `{ year: "2025" }`).

> Update the YAML files in the `table-configs` directory to change the view; no code edits required.

Example configuration (`table-configs/Sales_Team.table-config.yaml`):
```yaml
table: default.sales
dimensions:
  - name: Sales Team
  - name: Sales Manager
columns:
  - name: Decimal Cases
    agg: sum
    format: "#,##0.00"
  - name: Revenue
    agg: sum
    format: "$#,##0.00"
  - name: Bottles
    agg: sum
    format: "#,##0"
  - name: Cases
    agg: sum
    format: "#,##0"
sort:
  - by: Sales Team
    dir: asc
  - by: Sales Manager
    dir: asc
totals: top
filters:
  whereSQL: "`Ship Year` = {year:String}"
  params:
    year: "2025"
```

## How it works

### 1) Read the config (server-side)
- The server loads the YAML configuration file on each request.
- It validates essentials (table name, at least one column, any required filter parameters).
- No credentials are stored here; they remain in environment variables.

### 2) Build and run the ClickHouse query (server-side)
- The server constructs a `SELECT` with:
  - All **dimensions** as raw columns.
  - All **columns** as aggregated expressions (default `sum` unless specified).
- It adds:
  - A **WHERE** clause from `filters.whereSQL`, binding values from `filters.params`.
  - A **GROUP BY** over all dimensions.
  - **Grand totals** via `WITH TOTALS` when `totals` ≠ `none`.
  - An **ORDER BY** based on `sort`.
- The query runs through ClickHouse's HTTP API with `FORMAT JSON`. Credentials are sent server-to-server only.

### 3) Create and display the table
- The response includes **data rows** and, when requested, a **`totals` object**.
- The server inserts the totals row at the **top** or **bottom** (or omits it) according to the `totals` setting and labels it clearly (e.g., "Totals").
- The page renders a responsive table:
  - Dimension columns first, then values in the configured order.
  - Excel-style number formats (e.g., `#,##0.00`, `$#,##0.00`) applied when displaying values.
  - Right-aligned numeric cells and clean styling via Pico.css.

## Security & operations
- **Credentials**: Set `CLICKHOUSE_URL`, `CLICKHOUSE_USER`, and `CLICKHOUSE_PASSWORD` as environment variables; they are never exposed to the browser.
- **Safety**: Filter parameters are bound (not string-concatenated), avoiding SQL injection.
- **Performance**: Add a light server-side cache if the 2025 view is static for a session; invalidate when the config changes.

## Why this approach
- The **config** owns *what* to show (fields, formats, totals, sort, filters).
- The **server** owns *how* to query (safe, parameterized SQL).
- The **view** stays simple, fast, and easy to adjust without touching application code.

## Project Structure
```
├── components/        # Reusable UI components
├── routes/            # Page routes and API endpoints
├── static/            # Static assets (CSS, images, etc.)
├── table-configs/     # YAML configuration files for table views
├── deno.json          # Task runner and configuration
└── README.md          # Project documentation
```
