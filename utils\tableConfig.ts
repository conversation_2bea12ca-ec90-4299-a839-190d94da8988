import { queryClickHouse } from "./clickhouse.ts";
import * as yaml from "https://deno.land/std@0.217.0/yaml/mod.ts";

export type TableConfig = {
  table: string;
  dimensions: Array<{ name: string }>;
  columns: Array<{ 
    name: string; 
    label?: string;  
    agg?: "sum" | "avg" | "min" | "max" | "count" | "count_distinct"; 
    format?: string 
  }>;
  sort?: Array<{ by: string; dir?: "asc" | "desc" }>;
  totals?: "top" | "bottom" | "none";
  filters?: { whereSQL?: string; params?: Record<string, unknown> };
};

function qt(id: string) {
  // Quote ClickHouse identifiers with backticks, escape backticks inside
  return "`" + id.replaceAll("`", "``") + "`";
}

function toAlias(name: string) {
  return name
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "_")
    .replace(/^_+|_+$/g, "");
}

export async function loadTableConfig(path = new URL("../table-configs/Sales_Team.table-config.yaml", import.meta.url)) {
  const text = await Deno.readTextFile(path);
  const fileExt = path.pathname.split('.').pop()?.toLowerCase();
  
  let cfg: TableConfig;
  if (fileExt === 'yaml' || fileExt === 'yml') {
    cfg = yaml.parse(text) as TableConfig;
  } else if (fileExt === 'json') {
    cfg = JSON.parse(text) as TableConfig;
  } else {
    throw new Error(`Unsupported file format: ${fileExt}. Only YAML and JSON are supported.`);
  }
  
  if (!cfg.table) throw new Error("Config missing 'table'");
  if (!cfg.columns || cfg.columns.length === 0) throw new Error("Config must include at least one column");
  if (!cfg.dimensions) cfg.dimensions = [];
  if (!cfg.totals) cfg.totals = "top";
  return cfg;
}

export async function getConfiguredTable(cfg: TableConfig) {
  const dimDefs = cfg.dimensions.map((d) => ({ 
    title: d.name, 
    alias: toAlias(d.name), 
    name: d.name 
  }));
  
  const colDefs = cfg.columns.map((c) => ({
    title: c.label || c.name,  
    alias: toAlias(c.name), 
    name: c.name, 
    agg: c.agg ?? "sum", 
    format: c.format,
    label: c.label  
  }));

  const selectDims = dimDefs.map((d) => `${qt(d.name)} AS ${d.alias}`);
  const selectCols = colDefs.map((m) => {
    if (m.agg === "count_distinct") {
      return `count(distinct ${qt(m.name)}) AS ${m.alias}`;
    }
    return `${m.agg}(${qt(m.name)}) AS ${m.alias}`;
  });
  const selectSQL = [...selectDims, ...selectCols].join(",\n  ");

  const groupBySQL = dimDefs.length ? `\nGROUP BY ${dimDefs.map((d) => qt(d.name)).join(", ")}` : "";
  const totalsSQL = cfg.totals && cfg.totals !== "none" ? " WITH TOTALS" : "";

  const whereSQL = cfg.filters?.whereSQL ? `\nWHERE ${cfg.filters.whereSQL}` : "";
  const orderBy = (cfg.sort ?? []).map((s) => {
    const alias = [...dimDefs, ...colDefs].find((d) => d.title === s.by)?.alias ?? toAlias(s.by);
    const dir = (s.dir ?? "asc").toLowerCase() === "desc" ? "DESC" : "ASC";
    return `${alias} ${dir}`;
  });
  const orderBySQL = orderBy.length ? `\nORDER BY ${orderBy.join(", ")}` : "";

  const sql = `SELECT\n  ${selectSQL}\nFROM ${cfg.table}${whereSQL}${groupBySQL}${totalsSQL}${orderBySQL}\nSETTINGS totals_mode = 'after_having_auto'`;

  type Row = Record<string, unknown>;
  type Totals = Record<string, number>;
  const { data, totals } = await queryClickHouse<Row, Totals>(sql, cfg.filters?.params ?? {});

  const columns = [
    ...dimDefs.map((d) => ({ key: d.alias, title: d.title, isNumeric: false as const })),
    ...colDefs.map((m) => ({ key: m.alias, title: m.title, isNumeric: true as const, format: m.format })),
  ];

  let totalsRow: Record<string, unknown> | undefined;
  if (cfg.totals && cfg.totals !== "none" && totals) {
    totalsRow = {} as Record<string, unknown>;
    if (dimDefs.length) {
      totalsRow[dimDefs[0].alias] = "Totals";
      for (let i = 1; i < dimDefs.length; i++) totalsRow[dimDefs[i].alias] = "";
    }
    for (const m of colDefs) totalsRow[m.alias] = totals[m.alias] ?? 0;
  }

  return { columns, rows: data as Array<Record<string, unknown>>, totalsRow, totalsPlacement: cfg.totals ?? "top" };
}

export async function getTableConfigs(): Promise<Array<{name: string, path: string}>> {
  const configsDir = new URL("../table-configs/", import.meta.url);
  const configFiles: Array<{name: string, path: string}> = [];
  
  for await (const dirEntry of Deno.readDir(configsDir)) {
    if (dirEntry.isFile && (dirEntry.name.endsWith('.yaml') || dirEntry.name.endsWith('.yml'))) {
      const name = dirEntry.name.replace(/\.table-config\.(ya?ml|json)$/, '');
      const path = new URL(`../table-configs/${dirEntry.name}`, import.meta.url);
      configFiles.push({ name, path: path.pathname });
    }
  }
  
  return configFiles;
}
